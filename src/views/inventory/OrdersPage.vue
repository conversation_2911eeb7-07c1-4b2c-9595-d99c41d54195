<template>
  <div class="start-bill-page">
    <!-- 筛选条件区域 -->
    <n-card title="筛选条件" class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">销售日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">车辆品牌</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.vehicleCategory"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in vehicleCategoryOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">订单状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.orderStatus"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in orderStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">销售单位</div>
          <div class="filter-options org-selector-container">
            <n-button
              type="primary"
              ghost
              @click="showOrgSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon><component :is="BusinessOutlineIcon" /></n-icon>
              </template>
              {{ selectedOrgText }}
            </n-button>
            <n-button
              v-if="filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0"
              type="error"
              ghost
              size="small"
              @click="clearOrgSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
            <!-- 显示选中的机构标签 - 在清空按钮右侧 -->
            <div
              v-if="filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0"
              class="selected-orgs-tags"
            >
              <n-tag
                v-for="org in filterForm.invoiceOrgs"
                :key="org.id"
                type="success"
                size="small"
                closable
                @close="removeOrg(org)"
                style="margin-left: 4px"
              >
                {{ org.orgName }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><component :is="RefreshOutlineIcon" /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><component :is="AddOutlineIcon" /></n-icon>
          </template>
          销售订单
        </n-button>

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="输入关键字进行搜索"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><component :is="SearchOutlineIcon" /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="(row) => row.id"
          :max-height="tableMaxHeight"
          virtual-scroll
          virtual-scroll-x
          :scroll-x="scrollX"
          :min-row-height="48"
          :height-for-row="() => 48"
          virtual-scroll-header
          :header-height="48"
          striped
          size="medium"
          @update:checked-row-keys="handleSelectionChange"
        />
      </div>

      <!-- 分页组件 - 固定在表格底部 -->
      <div class="pagination-container">
        <!-- 调试信息 -->
        <div
          v-if="false"
          style="
            margin-bottom: 8px;
            font-size: 12px;
            color: #666;
            background: yellow;
            padding: 4px;
          "
        >
          分页调试: itemCount={{ pagination.itemCount }}, page={{
            pagination.page
          }}, pageSize={{ pagination.pageSize }}, dataLength={{
            filteredData.length
          }}, tableHeight={{ tableMaxHeight }}
        </div>
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :item-count="Math.max(pagination.itemCount, 1)"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          size="medium"
          size-picker-option-text="条/页"
          page-size-option="每页"
          :prefix="() => `共 ${pagination.itemCount} 条`"
          :display-order="['prefix', 'pages', 'size-picker']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <order-edit-modal-new
      ref="orderEditModalRef"
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :title="dialogTitle"
      :vehicle-category-options="
        vehicleCategoryOptions.filter((item) => item.value !== null)
      "
      :order-status-options="
        orderStatusOptions.filter((item) => item.value !== null)
      "
      @save="handleSaveSuccess"
      @cancel="dialogVisible = false"
    />

    <!-- 详情弹窗 -->
    <order-detail-modal
      v-model:visible="detailDialogVisible"
      :id="currentDetailId"
    />

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择销售单位"
      business-permission="can_sell"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />
  </div>
</template>

<script setup>
import { useOrdersPage } from "./OrdersPage.js";

// 使用页面逻辑
const {
  // 图标
  SearchOutlineIcon,
  RefreshOutlineIcon,
  AddOutlineIcon,
  BusinessOutlineIcon,

  // 组件
  OrderDetailModal,
  OrderEditModalNew,
  BizOrgSelector,

  // 状态
  tableRef,
  orderEditModalRef,
  loading,
  dialogVisible,
  dialogTitle,
  isEdit,
  detailDialogVisible,
  currentDetailId,
  showOrgSelector,

  // 数据
  dateRangeOptions,
  vehicleCategoryOptions,
  orderStatusOptions,
  filterForm,
  pagination,
  filteredData,
  columns,
  scrollX,
  tableMaxHeight,
  selectedOrgText,

  // 方法
  refreshData,
  handleDateRangeChange,
  handleCustomDateChange,
  handleSearch,
  showAddDialog,
  handleSaveSuccess,
  handleSelectionChange,
  handlePageChange,
  handlePageSizeChange,

  // 机构选择器相关方法
  handleOrgSelect,
  handleOrgCancel,
  clearOrgSelection,
  removeOrg,
} = useOrdersPage();
</script>

<style lang="scss" scoped>
@use "./OrdersPage.scss";
</style>